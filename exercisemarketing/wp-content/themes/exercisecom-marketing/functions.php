<?php
/**
 * Theme for the Postlight Headless WordPress Starter Kit.
 *
 * Read more about this project at:
 * https://postlight.com/trackchanges/introducing-postlights-wordpress-react-starter-kit
 *
 * @package  Postlight_Headless_WP
 */

// Frontend origin.
require_once 'inc/frontend-origin.php';

// ACF commands.
require_once 'inc/class-acf-commands.php';

// Logging functions.
require_once 'inc/log.php';

// CORS handling.
require_once 'inc/cors.php';

// Admin modifications.
require_once 'inc/admin.php';

// Add Menus.
require_once 'inc/menus.php';

// Add Headless Settings area.
require_once 'inc/acf-options.php';

// Add GraphQL resolvers.
require_once 'inc/graphql/resolvers.php';

require_once 'inc/rest.php';

// Add more support for image size customization
add_theme_support( 'post-thumbnails' );
add_image_size( 'sm_400', 400, 9999 ); // Unlimited Height Mode
add_image_size( 'md_800', 800, 9999 ); // Unlimited Height Mode
add_image_size( '1x_1200', 1200, 9999 ); // Unlimited Height Mode
add_image_size( '2x_1800', 1800, 9999 ); // Unlimited Height Mode

// change WordPress API URL to HOME URL
add_filter('rest_url', 'ex_home_url_as_api_url');
function ex_home_url_as_api_url($url) {
	$url = str_replace(home_url(),site_url() , $url);
	return $url;
}

add_filter('home_url', 'ex_force_home_url', 9999, 2);

function ex_force_home_url($home_url, $path) {
	if( !str_contains(get_site_url(), 'staging') ) {
		$home_url = str_replace("johntest.exercise", "www.exercise", $home_url);
		$home_url = str_replace("staging.exercise", "www.exercise", $home_url);
		$home_url = str_replace("cms.exercise", "www.exercise", $home_url);

	}

//	echo $home_url;
	return $home_url;
}

add_filter( 'option_home', function( $home_url ){
	if( !str_contains(get_site_url(), 'staging') ) {
		$home_url = str_replace("johntest.exercise", "www.exercise", $home_url);
		$home_url = str_replace("staging.exercise", "www.exercise", $home_url);
		$home_url = str_replace("cms.exercise", "www.exercise", $home_url);
	}
	return $home_url;
});

// ACF Blocks for Gutenberg
add_action('acf/init', 'my_acf_init');
function my_acf_init() {
    
    // check function exists
    if( function_exists('acf_register_block') ) {
        
        // register a testimonial block
        acf_register_block(array(
            'name'              => 'testimonial',
            'title'             => __('Testimonial'),
            'description'       => __('A custom testimonial block that will display full width (landscape) when displayed on desktop.'),
            'render_callback'   => 'my_acf_block_render_callback',
            'category'          => 'formatting',
            'icon'              => 'admin-comments',
            'keywords'          => array( 'testimonial', 'quote' ),
        ));
    }
}

function my_acf_block_render_callback( $block ) {
    
	// convert name ("acf/testimonial") into path friendly slug ("testimonial")
	$slug = str_replace('acf/', '', $block['name']);
	
	// include a template part from within the "template-parts/block" folder
	if( file_exists( get_theme_file_path("/template-parts/block/content-testimonial.php") ) ) {
			include( get_theme_file_path("/template-parts/block/content-testimonial.php") );
	}
}

function custom_rest_api_check_post_type($response, $post, $request) {
    // Check the post type
     $post_type = get_post_type($post);

     // Add the post type to the API response
     $response->data['content']['rendered'] = $response->data['content']['rendered'] . do_shortcode('[rp4wp]');
     return $response;
}
add_filter('rest_prepare_alternative', 'custom_rest_api_check_post_type', 10, 3);
add_filter('rest_prepare_grow', 'custom_rest_api_check_post_type', 10, 3);
add_filter('rest_prepare_comparison', 'custom_rest_api_check_post_type', 10, 3);


/* Current Year Stuff */
function title_set_current_year($title) {
    return str_replace('#current_year', date("Y"), $title);
}
add_filter('the_title', 'title_set_current_year');
add_filter('rp4wp_post_title', 'title_set_current_year');
add_filter('get_the_archive_title', 'title_set_current_year');

/**
 * #current_month and #current_year tag Replacement on breadcrumbs
 */
function aioseo_breadcrumbs_current_month_year_replacemnets($crumbs) {
    foreach ($crumbs as $key => $crumb) {
        $crumb['label'] = str_replace(['#current_month', '#current_year'], [date("F"), date("Y")], $crumb['label']);
        $crumbs[$key] = $crumb;
    }
    return $crumbs;
}
add_filter('aioseo_breadcrumbs_trail', 'aioseo_breadcrumbs_current_month_year_replacemnets');

function customize_header_field_value($value, $post_id, $field) {
    return str_replace('#current_year', date("Y"), $value);
}

// Hook into the acf/format_value filter
add_filter('acf/format_value/key=field_6376e42087dc4_field_61b98a1b01513', 'customize_header_field_value', 10, 3);

function phoenix_remove_current_year_from_slug($post_id) {
    // verify post is not a revision
    if (!wp_is_post_revision($post_id)) {
        $post = get_post($post_id);
        if ($post instanceof WP_Post) {
            // check if smart tag is in the post title
            $count = preg_match("/#current_year/", $post->post_title);
            if ($count === 1) {
                // get new slug without current_year in it
                $count = 0;
                $slug = $post->post_name;
                $new_slug = str_replace("-current_year", "", $slug, $count);

                // update slug if we made changes
                if ($count !== 0) {
                    // unhook this function to prevent infinite looping
                    remove_action('save_post', 'phoenix_remove_current_year_from_slug');
                    // update the post slug
                    wp_update_post([
                        'ID' => $post_id,
                        'post_name' => $new_slug
                    ]);
                    // re-hook this function
                    add_action('save_post', 'phoenix_remove_current_year_from_slug');
                }
            }
        }
    }
}
add_action('save_post', 'phoenix_remove_current_year_from_slug');


// Updated at 05/08/2025
function wp_headless_preview_link($link, $post) {
    $map = [
        'page'           => '',
        'post'           => '/blog',
        'grow'           => '/grow',
        'alternative'    => '/grow/alternative',
        'solutions'      => '/solutions',
        'platform'       => '/platform',
        'support'        => '/support',
        'topics'         => '/support/topics',
        'comparison'     => '/grow/comparison',
        'thank-you'      => '/thank-you',
        'land'           => '/demo',
        'designed_page'  => '',
    ];

    $base = isset($map[$post->post_type])
        ? rtrim($map[$post->post_type], '/')
        : $post->post_type; // fallback

    if ($post->post_type === 'designed_page' && $post->post_name === 'pricing') {
        $base = '/pricing';
    }

    $nameOrID = $post->post_name ?: $post->ID;
    $slug = $base . '/' . $nameOrID;

    $preview = add_query_arg([
        'secret' => 'df98a4e217cf417ca6be9b6ea76d3fc08c0ad7d1d0833fd64395e7fcda893c8e',
        'slug'   => $slug,
    ], 'https://nextjs.staging.exercise.com/api/preview');
    return $preview;
}
add_filter('preview_post_link', 'wp_headless_preview_link', 10, 2);

add_action( 'admin_footer', function() {
    ?>
    <script>
        (function(){
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((m) => {
                    if (m.addedNodes.length || m.removedNodes.length) {
                        const btn = document.querySelector('.editor-post-preview, .editor-header__post-preview-button');
                        const view = document.querySelector('.components-button.is-compact.has-icon[aria-label="View Page"], #post-preview');

                        if (btn && view) {
                            const newHref = "<?php echo esc_js(get_preview_post_link()) ?>";
                            btn.href = newHref;
                            btn.target = "_blank";
                            view.href = newHref;
                            view.target = "_blank";
                            console.log('MutationObserver set preview:', newHref);
                        }
                    }
                });
            });

            observer.observe(document.body, { subtree: true, childList: true });
        })();
    </script>
    <?php
});

add_action('rest_api_init', function () {
    register_rest_route('preview/v1', '/(?P<type>[a-zA-Z0-9_-]+)/(?P<id>\d+)', [
        'methods' => 'GET',
        'callback' => 'get_preview_data',
        'permission_callback' => function () {
            return current_user_can('edit_posts');
        },
    ]);
});



function get_preview_data($request) {
    $id = (int) $request['id'];
    $type = sanitize_key($request['type']);
    $acf_format = $request->get_param('acf_format');
    $context = $request->get_param('context') ?: 'view';
    $force_preview = $request->get_param('force_preview') === 'true';

    $post = get_post($id);
    if (!$post || $post->post_type !== $type) {
        return new WP_Error('invalid_post', 'Invalid post or post type.', ['status' => 404]);
    }

    $preview_post = null;
    $is_preview = false;

    // 1. First try to get autosave
    $autosave = wp_get_post_autosave($id);
    if ($autosave) {
        $preview_post = $autosave;
        $is_preview = true;
    }

    // 2. Then try to get latest revision
    if (!$preview_post) {
        $revisions = wp_get_post_revisions($id, ['order' => 'DESC', 'orderby' => 'ID']);
        if (!empty($revisions)) {
            $latest_revision = reset($revisions);
            // Only use revision if it's different from published version
            if ($latest_revision->post_modified > $post->post_modified) {
                $preview_post = $latest_revision;
                $is_preview = true;
            }
        }
    }

    // 3. Use draft post
    if (!$preview_post && in_array($post->post_status, ['draft', 'pending', 'future', 'private'])) {
        $preview_post = $post;
        $is_preview = true;
    }

    // 4. If force_preview is true or no preview found, use the original post
    if (!$preview_post || $force_preview) {
        $preview_post = $post;
        // Only consider it a preview if it's not published or if forced
        $is_preview = ($post->post_status !== 'publish') || $force_preview;
    }

    if (!$preview_post) {
        return new WP_Error('no_preview', 'No preview available.', ['status' => 404]);
    }

    // Get post type object for additional data
    $post_type_object = get_post_type_object($preview_post->post_type);

    // Build response in WordPress REST API format
    $response = [
        'id' => $preview_post->ID,
        'date' => mysql2date('c', $preview_post->post_date, false),
        'date_gmt' => mysql2date('c', $preview_post->post_date_gmt, false),
        'guid' => [
            'rendered' => get_the_guid($preview_post),
            'raw' => $preview_post->guid
        ],
        'modified' => mysql2date('c', $preview_post->post_modified, false),
        'modified_gmt' => mysql2date('c', $preview_post->post_modified_gmt, false),
        'slug' => $preview_post->post_name,
        'status' => $preview_post->post_status,
        'type' => $preview_post->post_type,
        'link' => get_permalink($preview_post),
        'title' => [
            'rendered' => get_the_title($preview_post),
            'raw' => $preview_post->post_title
        ],
        'content' => [
            'rendered' => apply_filters('the_content', $preview_post->post_content),
            'raw' => $preview_post->post_content,
            'protected' => false
        ],
        'excerpt' => [
            'rendered' => apply_filters('the_excerpt', get_post_field('post_excerpt', $preview_post, 'display')),
            'raw' => $preview_post->post_excerpt,
            'protected' => false
        ],
        'author' => (int) $preview_post->post_author,
        'featured_media' => (int) get_post_thumbnail_id($preview_post->ID),
        'comment_status' => $preview_post->comment_status,
        'ping_status' => $preview_post->ping_status,
        'sticky' => is_sticky($preview_post->ID),
        'template' => get_page_template_slug($preview_post),
        'format' => get_post_format($preview_post) ?: 'standard',
        'meta' => [],
        '_links' => [],
        '_embedded' => [],
        // Add preview-specific data
        'is_preview' => $is_preview,
        'original_post_id' => $post->ID,
        'preview_type' => $is_preview ? ($preview_post->post_type === 'revision' ? 'revision' : ($autosave ? 'autosave' : 'draft')) : 'published'
    ];

    // Handle taxonomies
    $taxonomies = wp_list_filter(get_object_taxonomies($preview_post->post_type, 'objects'), ['show_in_rest' => true]);
    foreach ($taxonomies as $taxonomy) {
        $terms = get_the_terms($preview_post, $taxonomy->name);
        $response[$taxonomy->rest_base] = $terms ? array_values(wp_list_pluck($terms, 'term_id')) : [];
    }

    // Handle meta fields
    $meta_fields = get_registered_meta_keys('post', $preview_post->post_type);
    foreach ($meta_fields as $meta_key => $meta_config) {
        if (!empty($meta_config['show_in_rest'])) {
            $meta_value = get_post_meta($preview_post->ID, $meta_key, $meta_config['single']);

            // If no meta for revision/autosave, try original post
            if (empty($meta_value) && $preview_post->ID !== $post->ID) {
                $meta_value = get_post_meta($post->ID, $meta_key, $meta_config['single']);
            }

            $response['meta'][$meta_key] = $meta_value;
        }
    }

    // Handle ACF fields
    if (function_exists('get_fields')) {
        $acf_fields = get_fields($preview_post->ID);

        // If no ACF fields for revision/autosave, try original post
        $is_revision_or_autosave = ($preview_post->post_type === 'revision' || wp_is_post_autosave($preview_post));
        if ($is_revision_or_autosave && (!$acf_fields || count($acf_fields) === 0) && $preview_post->ID !== $post->ID) {
            $acf_fields = get_fields($post->ID);
        }

        if ($acf_fields) {
            if ($acf_format === 'all') {
                $response['acf'] = $acf_fields;
            } else {
                // Format ACF fields for REST API
                $formatted_acf = [];
                foreach ($acf_fields as $field_name => $field_value) {
                    // Get field object for proper formatting
                    $field_object = get_field_object($field_name, $preview_post->ID);
                    if (!$field_object && $preview_post->ID !== $post->ID) {
                        $field_object = get_field_object($field_name, $post->ID);
                    }

                    if ($field_object) {
                        // Apply ACF formatting filters
                        $formatted_value = apply_filters('acf/format_value', $field_value, $preview_post->ID, $field_object);
                        $formatted_value = apply_filters("acf/format_value/type={$field_object['type']}", $formatted_value, $preview_post->ID, $field_object);
                        $formatted_value = apply_filters("acf/format_value/name={$field_object['name']}", $formatted_value, $preview_post->ID, $field_object);
                        $formatted_value = apply_filters("acf/format_value/key={$field_object['key']}", $formatted_value, $preview_post->ID, $field_object);

                        $formatted_acf[$field_name] = $formatted_value;
                    } else {
                        $formatted_acf[$field_name] = $field_value;
                    }
                }
                $response['acf'] = $formatted_acf;
            }
        }
    }

    // Build _embedded data (similar to previous implementation)
    if ($context === 'embed' || strpos($request->get_param('_embed') ?: '', 'author') !== false) {
        $author_id = $preview_post->post_author;
        $response['_embedded']['author'] = [[
            'id' => (int) $author_id,
            'name' => get_the_author_meta('display_name', $author_id),
            'url' => get_author_posts_url($author_id),
            'description' => get_the_author_meta('description', $author_id),
            'link' => get_author_posts_url($author_id),
            'slug' => get_the_author_meta('user_nicename', $author_id),
            'avatar_urls' => rest_get_avatar_urls($author_id),
        ]];
    }

    if ($context === 'embed' || strpos($request->get_param('_embed') ?: '', 'wp:featuredmedia') !== false) {
        $featured_media_id = get_post_thumbnail_id($preview_post->ID);
        if (!$featured_media_id && $preview_post->ID !== $post->ID) {
            $featured_media_id = get_post_thumbnail_id($post->ID);
        }

        if ($featured_media_id) {
            $media_post = get_post($featured_media_id);
            if ($media_post) {
                $image_sizes = [];

                // Get all image sizes
                foreach (get_intermediate_image_sizes() as $size) {
                    $image_data = wp_get_attachment_image_src($featured_media_id, $size);
                    if ($image_data) {
                        $image_sizes[$size] = [
                            'file' => basename($image_data[0]),
                            'width' => $image_data[1],
                            'height' => $image_data[2],
                            'source_url' => $image_data[0]
                        ];
                    }
                }

                $response['_embedded']['wp:featuredmedia'] = [[
                    'id' => $featured_media_id,
                    'source_url' => wp_get_attachment_url($featured_media_id),
                    'alt_text' => get_post_meta($featured_media_id, '_wp_attachment_image_alt', true),
                    'media_details' => [
                        'sizes' => $image_sizes
                    ],
                ]];
            }
        }
    }

    // Build _links
    $base_url = rest_url('wp/v2/');
    if ($post_type_object && !empty($post_type_object->rest_base)) {
        $response['_links']['self'] = [[
            'href' => $base_url . $post_type_object->rest_base . '/' . $preview_post->ID
        ]];

        $response['_links']['collection'] = [[
            'href' => $base_url . $post_type_object->rest_base
        ]];
    }

    return rest_ensure_response($response);
}


